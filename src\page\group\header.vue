<template>
  <div class="head ">
    <div class="head_actions head_left">
      <div style="color:#fff;height: 40px;line-height: 40px;display: flex;">
        <img style="width:22px;height:22px;margin-top:10px" src="../../../public/img/logo.png">
        <span>&nbsp;{{ $website.title }}</span>
        <el-select size="mini" style="width: 120px;margin-left: 10px;" @change="change_langCode" v-model="langCode" placeholder="请选择">
          <el-option
            label="中文"
            value="zh">
          </el-option>
          <el-option
            label="English"
            value="en">
          </el-option>
            <el-option
            label="越南语"
            value="vi">
          </el-option>
        </el-select>
        <!-- {{$t('lang.intro')}} -->
      </div>
      <!-- <div
       class="head_btn"
       :class="{ 'head_btn--active': contain.menuShow }"
       @click="handleFlag('menuShow')"
     >
       <el-tooltip effect="dark" content="图层" placement="top">
         <i class="el-icon-s-operation"></i>
       </el-tooltip>
     </div> -->
      <!-- <div
       class="head_btn"
       :class="{ 'head_btn--active': contain.paramsShow }"
       @click="handleFlag('paramsShow')"
     >
       <el-tooltip effect="dark" content="操作" placement="top">
         <i class="el-icon-setting"></i>
       </el-tooltip>
     </div> -->
     <!-- LargeScreenName:'大屏名称',
			TemplateName:'模板名称',
			Preview:'预览',
			SaveCover:'保存封面',
			SaveConfiguration:'保存配置',
			Import:'导入',
			Export:'导出',
			Undo:'撤销' -->
    </div>
    <div class="head_info ">
      <!-- <i class="el-icon-data-analysis"></i> -->
      <span v-if="cankanType == 0">{{$t('page.header.LargeScreenName')}}-{{ contain.config.name ? contain.config.name : title }}</span>
      <span v-else>{{$t('page.header.TemplateName')}}-{{ contain.config.name ? contain.config.name : title }}</span>
    </div>
    <div style="width: 500px;overflow: hidden;" class="head_actions border-red1">
      <div class="head_btn" style="margin-right: 10px;" @click="handleView">
        <el-tooltip effect="dark" :content="`${$t('page.header.Preview')}`" placement="top">
          <i class="iconfont icon-view"></i>
        </el-tooltip>
        {{$t('page.header.Preview')}}
      </div>
     
      <div class="" style="width: 800px;color:#fff;" @click="handleBuild">
        <el-tooltip effect="dark" :content="`${$t('page.header.SaveCover')}`" placement="top">
          <i class="iconfont icon-build"></i>
        </el-tooltip>
        {{$t('page.header.SaveCover')}}
      </div>
      <div class="" style="width: 800px;color:#fff;" @click="handleSave">
        <el-tooltip effect="dark" :content="`${$t('page.header.SaveConfiguration')}`" placement="top">
          <i class="iconfont icon-build"></i>
        </el-tooltip>
        {{$t('page.header.SaveConfiguration')}}
      </div>
      <div class="head_btn" @click="$refs.result.show = true">
        <el-tooltip effect="dark" :content="`${$t('page.header.Import')}`" placement="top">
          <i class="el-icon-download"></i>
        </el-tooltip>
        {{$t('page.header.Import')}}
      </div>
      <div class="head_btn" @click="handleImg">
        <el-tooltip effect="dark" :content="`${$t('page.header.Export')}`" placement="top">
          <i class="el-icon-upload2"></i>
        </el-tooltip>
        {{$t('page.header.Export')}}
      </div>



      <!-- <div class="head_btn" @click="handleShare">
       <el-tooltip effect="dark"
                   content="分享"
                   placement="top">
         <i class="el-icon-share"></i>
       </el-tooltip>
       分享
     </div> -->
      <div class="head_btn" :disabled="!contain.canUndo" @click="contain.editorUndo">
        <el-tooltip effect="dark" :content="`${$t('page.header.Undo')}`" placement="top">
          <i class="el-icon-ice-cream-round"></i>
        </el-tooltip>
       {{$t('page.header.Undo')}}
      </div>
      <!-- <div
       class="head_btn"
       :disabled="!contain.canRedo"
       @click="contain.editorRedo"
     >
       <el-tooltip effect="dark" content="重做" placement="top">
         <i class="nav__icon el-icon-arrow-right"></i>
       </el-tooltip>
       重做
     </div> -->
    </div>
    <result ref="result"></result>
  </div>
</template>

<script>
import { url } from '@/config';
import { getLocalStorageStore } from '@/utils/setStore'
import result from './result';
import { updateComponent, uploadImg } from '@/api/visual'
import share from './share'
import keys from './keys'
export default {
  inject: ["contain"],
  provide() {
    return {
      contain: this.contain
    };
  },
  data() {
    return {
      langCode:'zh',
      title: '',
      cankanType: 0
    }
  },
  components: {
    result,
    share,
    keys
  },
  mounted() {
    let _language= getLocalStorageStore('language')
    if(_language=='en'){
      this.langCode='en'
    }else{
      this.langCode='zh'
    }
    setTimeout(() => {
      //debugger
      this.title = this.contain.obj?.visual?.CTITLE
      this.cankanType = this.contain.obj?.visual?.CKANBAN_TYPE
      console.log("this.cankanType:",this.cankanType);
      console.log(this.$route, 'this.$route----');
    }, 1000);

  },
  created() {


    if (this.$website.autoSave) {
      setInterval(() => {
        this.handleSave(undefined, false, false);
      }, this.$website.autoSaveTime)
    }

  },
  methods: {
    change_langCode(val){
      // console.log(this.$t('lang.intro'));
      this.$i18n.locale = val;
    },
    handleGoIndex() {
      this.$router.push({ path: '/' })
    },
    handleKeys() {
      this.$refs.keys.show = true;
    },
    handleReset() {
      this.contain.menuFlag = true;
      this.contain.setScale();
    },
    handleFlag(name) {
      this.contain[name] = !this.contain[name]
      this.$nextTick(() => {
        let screens = document.getElementById('screens')
        this.contain.setScale(screens.clientWidth);
      })
    },
    handleView() {
      let routeUrl = this.$router.resolve({
        path: "/view/" + this.contain.obj.visual.CID,
      });
      window.__POWERED_BY_QIANKUN__
        ? window.open(`/subapp${routeUrl.href}`, "_blank")
        : window.open(routeUrl.href, "_blank");
    },
    async handleBuild(fn) {
      let isFun = typeof fn === "function";
      this.contain.handleInitActive();
      const loading = this.$loading({
        lock: true,
        text: this.$t('page.header.SavingTheConfigurationCover'),//"正在保存配置封面，请稍后",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      this.$nextTick(() => {
        html2canvas(document.getElementById("content"), {
          useCORS: true,
          backgroundColor: null,
          allowTaint: true,
        }).then((canvas) => {
          function dataURLtoFile(dataurl, filename) {
            var arr = dataurl.split(","),
              mime = arr[0].match(/:(.*?);/)[1],
              bstr = atob(arr[1]),
              n = bstr.length,
              u8arr = new Uint8Array(n);
            while (n--) {
              u8arr[n] = bstr.charCodeAt(n);
            }
            return new File([u8arr], filename, { type: mime });
          }

          var file = dataURLtoFile(
            canvas.toDataURL("image/jpeg", 0.1),
            new Date().getTime() + ".jpg"
          );
          var array = []

          var reader = new FileReader();
          reader.readAsArrayBuffer(file);
          reader.onload = function () {
            var byts = new Uint8Array(this.result);
            array = Array.from(byts)

          };
          setTimeout(() => {
            uploadImg(array)
              .then((res) => {
                res = res.data
                const url = res.data.Content;
                const formdata = {
                  Kanban: {
                    CID: this.contain.obj.visual.CID,
                    CBACKGROUND_URL: url,
                    CTITLE: this.contain.config.name,
                    CGROUP_ID: this.contain.obj.visual.CGROUP_ID,
                    CKANBAN_TYPE: this.cankanType,
                    CWIDTH: this.contain.config.width,
                    CHEIGHT: this.contain.config.height,
                    CKANBAN_LABEL_ID: this.contain.obj.visual.CKANBAN_LABEL_ID
                  },
                  KanbanConfig: {
                    CID: this.contain.obj.config.CID,
                    CKANBAN_ID: this.contain.obj.visual.CID,
                    CDETAIL: JSON.stringify(this.contain.config),
                    CCOMPONET: JSON.stringify(this.contain.nav),
                  },
                };

                return updateComponent(formdata);
              })
              .then(() => {
                loading.close();
                if (isFun) {
                  fn();
                } else {
                  this.$message.success(`${this.$t('page.header.LargeScreenConfigurationCoverSavedSuccessfully')}`);//+"大屏配置封面保存成功"
                  this.$eventBus.$emit("updateKanBanList", `${this.$t('page.header.UpdateDashboardList')}`);
                }
              })
              .catch((err) => {
                console.log(err);
                this.$message.error(`${this.$t('page.header.LargeScreenConfigurationCoverFailedToBeSaved')}`);//+"大屏配置封面保存失败，请检查服务端配置"
                loading.close();
              });
          }, 1000);
        });
      });
    },
    async handleSave(fn) {
      let isFun = typeof fn === "function";
     
      this.contain.handleInitActive();
      let _bgIMGUrl = this.contain.obj.visual.CBACKGROUND_URL
      const loading = this.$loading({
        lock: true,
        text: this.$t('page.header.SavingConfiguration'),//"正在保存配置，请稍后",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      this.$nextTick(() => {
        const formdata = {
          Kanban: {
            CID: this.contain.obj.visual.CID,
            CBACKGROUND_URL: _bgIMGUrl,
            CTITLE: this.contain.config.name, 
            CGROUP_ID: this.contain.obj.visual.CGROUP_ID,
            CKANBAN_TYPE: this.cankanType,
            CWIDTH: this.contain.config.width,
            CHEIGHT: this.contain.config.height,
            CKANBAN_LABEL_ID: this.contain.obj.visual.CKANBAN_LABEL_ID
          },
          KanbanConfig: {
            CID: this.contain.obj.config.CID,
            CKANBAN_ID: this.contain.obj.visual.CID,
            CDETAIL: JSON.stringify(this.contain.config), 
            CCOMPONET: JSON.stringify(this.contain.nav), 
          },
        };

        updateComponent(formdata)
          .then(() => {
            loading.close();
            if (isFun) {
              fn();
            } else {
              this.$message.success(`${this.$t('page.header.LargeScreenConfigurationSavedSuccessfully')}`);//+"大屏配置保存成功"
              this.$eventBus.$emit("updateKanBanList", `${this.$t('page.header.UpdateDashboardList')}`);//"更新看板列表"
            }
          })
          .catch((err) => {
            console.log(err);
            this.$message.error(`${this.$t('page.header.PleaseCheckTheServerConfiguration')}`);//+"大屏配置保存失败，请检查服务端配置"
            loading.close();
          });
      });
    },
    handleImg() {
      this.$confirm(`${this.$t('page.header.WhetherToExportTheLargeScreenPicture')}?`, `${this.$t('page.header.Tip')}`, {
        confirmButtonText: `${this.$t('page.header.Confirm')}`,//'确定',
        cancelButtonText: `${this.$t('page.header.Cancel')}`,//'取消',
        type: 'warning'
      }).then(() => {
        this.$Screenshot(document.getElementById('content'), {
          useCORS: true,
          backgroundColor: null,
          logging: false,
        }).then(canvas => {
          this.downFile(canvas.toDataURL("image/jpeg", 0.8), new Date().getTime());
          this.$message.success(`${this.$t('page.header.PictureExportSuccessful')}`)//+'图片导出成功'
        });
      }).catch(() => {

      });

    },
    handleShare() {
      this.$refs.share.handleShow()
    }
  }
}
</script>

<style lang="scss">
.head {
  position: relative;
  height: 41px;
  padding-right: 8px;
  display: flex;
  z-index: 100;
  align-items: center;
  user-select: none;
  color: var(--datav-gui-font-color-base);
  border-bottom: var(--datav-border-dark);
  background: #1D1E1F;

  &_actions {
    position: absolute;
    top: 0;
    right: 8px;
    width: 470px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 40px;
  }

  &_left {
    left: 8px;
    justify-content: flex-start;
  }

  &_btn {
    margin-left: 4px;
    width: 30px;
    height: 34px;
    line-height: 36px;
    text-align: center;
    cursor: pointer;

    color: #fff;
    transition: 0.2s;

    i {
      color: #fff;
    }

    &--active {
      background-color: #2681ff;
    }
  }

  &_info {
    position: absolute;
    left: 50%;
    top: 0;
    transform: translateX(-50%);
    text-align: center;
    cursor: default;
    font-size: 14px;
    max-width: 500px;
    font-weight: bold;
    color: #fff;
    line-height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex;
    align-items: center;

    i {
      margin-right: 8px;
      font-size: 20px;
    }
  }
}
</style>
<style>
.head_btn {
  width: 430px
}
</style>
